{"permissions": {"allow": ["Bash(rm:*)", "Bash(npm run build:*)", "Bash(npx vite:*)", "Bash(npm install:*)", "Bash(npm run lint)", "Bash(npm run dev:*)", "Bash(timeout 5s npm run build)", "Bash(git reset:*)", "Bash(timeout 10s npm run dev)", "Bash(find:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm run test:*)", "Bash(npx tsc:*)", "WebFetch(domain:localhost)", "Bash(git checkout:*)", "Bash(git restore:*)", "Bash(node:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "mcp__ide__getDiagnostics", "Bash(ls:*)", "Bash(cd:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm init:*)", "Bash(npx ts-node:*)", "Bash(npm run start:*)", "<PERSON><PERSON>(chmod:*)", "Bash(grep:*)", "Bash(npm ls:*)", "Bash(npm run:*)", "Bash(kill:*)", "Bash(npm:*)", "Bash(./scripts/verify-image-path-fixes.sh:*)", "Bash(cp:*)", "<PERSON><PERSON>(git clean:*)", "<PERSON><PERSON>(source .env)", "Bash(FEISHU_APP_ID=cli_a8fa1d87c3fad00d FEISHU_APP_SECRET=CDfRPlOw8VRQrPyLnpzNvd5wBmu6wROp FEISHU_APP_TOKEN=J4dFbm5S9azofMsW702cSOVwnsh FEISHU_TABLE_ID=tblwdwrZMikMRyxq node dist/scripts/test-feishu-connection.js)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(timeout:*)", "Bash(python test_feishu_api.py:*)", "<PERSON><PERSON>(python3:*)", "Bash(npx tsx:*)"], "deny": []}}