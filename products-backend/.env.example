# 环境配置
NODE_ENV=development
PORT=3000

# MongoDB 配置
MONGODB_URI=********************************:port/database?authSource=admin

# MinIO 配置
MINIO_ENDPOINT=your-minio-host
MINIO_PORT=9000
MINIO_ACCESS_KEY=your-access-key
MINIO_SECRET_KEY=your-secret-key
MINIO_BUCKET=product-images

# Redis 配置 (可选，后续添加)
REDIS_URL=redis://host:port

# 日志配置
LOG_LEVEL=info

# 数据同步配置
SYNC_SCHEDULER_ENABLED=true
TIMEZONE=Asia/Shanghai
